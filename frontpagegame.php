<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>Virtual <PERSON><PERSON> - <PERSON></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Animation Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/TextPlugin.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>

    <!-- Particles.js for background effects -->
    <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>

    <style>
        :root {
            --primary-color: #ff6b9d;
            --secondary-color: #4ecdc4;
            --accent-color: #45b7d1;
            --dark-bg: #0a0a0a;
            --darker-bg: #050505;
            --text-light: #ffffff;
            --text-glow: #ff6b9d;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --neon-glow: 0 0 20px rgba(255, 107, 157, 0.5);
            --box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: var(--dark-bg);
            color: var(--text-light);
            overflow-x: hidden;
            min-height: 100vh;
            position: relative;
        }

        /* Particles Background */
        #particles-js {
            position: fixed;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            z-index: 1;
        }

        /* Main Container */
        .game-container {
            position: relative;
            width: 100%;
            max-width: 480px;
            height: 100vh;
            margin: 0 auto;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
            overflow: hidden;
            z-index: 2;
            border-radius: 0;
        }

        @media (min-width: 768px) {
            .game-container {
                max-width: 520px;
                height: 95vh;
                border-radius: 20px;
                border: 1px solid rgba(255, 255, 255, 0.1);
                box-shadow: var(--box-shadow);
                margin-top: 2.5vh;
            }
        }

        /* Animated Background Layers */
        .bg-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('assets/background/kota.png') center/cover no-repeat;
            filter: brightness(0.2) saturate(1.5);
            z-index: 1;
        }

        .bg-gradient {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                180deg,
                rgba(15, 23, 42, 0.1) 0%,
                rgba(15, 23, 42, 0.8) 60%,
                rgba(15, 23, 42, 0.95) 100%
            );
            z-index: 2;
        }

        /* Animated Stars Background */
        .stars-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 3;
            overflow: hidden;
        }

        .star {
            position: absolute;
            background: #fff;
            border-radius: 50%;
            animation: twinkle 2s ease-in-out infinite alternate;
        }

        .star.small {
            width: 1px;
            height: 1px;
            animation-duration: 1.5s;
        }

        .star.medium {
            width: 2px;
            height: 2px;
            animation-duration: 2s;
        }

        .star.large {
            width: 3px;
            height: 3px;
            animation-duration: 2.5s;
            box-shadow: 0 0 6px #fff;
        }

        @keyframes twinkle {
            0% {
                opacity: 0.3;
                transform: scale(1);
            }
            100% {
                opacity: 1;
                transform: scale(1.2);
            }
        }

        /* Laser Grid Animation */
        .laser-grid {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 4;
            opacity: 0.3;
        }

        .laser-line {
            position: absolute;
            background: linear-gradient(90deg, transparent, #ff6b9d, transparent);
            height: 1px;
            width: 100%;
            animation: laser-sweep 4s linear infinite;
        }

        .laser-line.vertical {
            background: linear-gradient(0deg, transparent, #4ecdc4, transparent);
            width: 1px;
            height: 100%;
            animation: laser-sweep-vertical 5s linear infinite;
        }

        @keyframes laser-sweep {
            0% {
                transform: translateX(-100%);
                opacity: 0;
            }
            50% {
                opacity: 1;
            }
            100% {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        @keyframes laser-sweep-vertical {
            0% {
                transform: translateY(-100%);
                opacity: 0;
            }
            50% {
                opacity: 1;
            }
            100% {
                transform: translateY(100%);
                opacity: 0;
            }
        }

        /* Lightning Effects */
        .lightning-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 5;
            pointer-events: none;
        }

        .lightning {
            position: absolute;
            width: 2px;
            background: linear-gradient(0deg, transparent, #45b7d1, #fff, #45b7d1, transparent);
            opacity: 0;
            animation: lightning-flash 3s ease-in-out infinite;
        }

        @keyframes lightning-flash {
            0%, 90%, 100% {
                opacity: 0;
                transform: scaleY(0);
            }
            5%, 10% {
                opacity: 1;
                transform: scaleY(1);
                box-shadow: 0 0 20px #45b7d1, 0 0 40px #45b7d1;
            }
        }

        /* Header Section */
        .header {
            position: relative;
            z-index: 10;
            text-align: center;
            padding: 60px 20px 40px;
        }

        .game-logo {
            font-family: 'Orbitron', monospace;
            font-size: 2.5rem;
            font-weight: 900;
            background: var(--gradient-secondary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: var(--neon-glow);
            margin-bottom: 10px;
            opacity: 0;
            transform: translateY(-50px);
        }

        .game-subtitle {
            font-size: 1.1rem;
            color: var(--secondary-color);
            font-weight: 300;
            letter-spacing: 2px;
            opacity: 0;
            transform: translateY(30px);
            margin-bottom: 20px;
        }

        /* Character Preview */
        .character-preview {
            position: relative;
            z-index: 5;
            height: 40vh;
            display: flex;
            justify-content: center;
            align-items: flex-end;
            margin: 20px 0;
            opacity: 0;
        }

        /* Game Menu - Bottom positioned with increased height */
        .game-menu {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 10;
            padding: 0 30px 80px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 200px;
        }

        .start-game-container {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 35px;
            min-height: 120px;
            justify-content: center;
        }

        .start-game-text {
            font-family: 'Orbitron', monospace;
            font-size: 3rem;
            font-weight: 900;
            background: linear-gradient(45deg, #ff6b9d, #4ecdc4, #45b7d1, #ff6b9d);
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            cursor: pointer;
            position: relative;
            opacity: 0;
            transform: translateY(100px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            text-align: center;
            letter-spacing: 5px;
            animation: gradient-shift 3s ease infinite, pulse-glow 2s ease-in-out infinite;
            line-height: 1.2;
            padding: 20px 0;
        }

        .start-game-text:hover {
            transform: translateY(-10px) scale(1.1);
            letter-spacing: 6px;
        }

        .start-game-text:active {
            transform: translateY(-5px) scale(1.05);
        }

        /* Gradient animation */
        @keyframes gradient-shift {
            0%, 100% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
        }

        /* Enhanced pulsing glow */
        @keyframes pulse-glow {
            0%, 100% {
                filter: drop-shadow(0 0 20px rgba(255, 107, 157, 0.6))
                        drop-shadow(0 0 40px rgba(78, 205, 196, 0.4));
            }
            50% {
                filter: drop-shadow(0 0 30px rgba(255, 107, 157, 0.9))
                        drop-shadow(0 0 60px rgba(78, 205, 196, 0.7))
                        drop-shadow(0 0 80px rgba(69, 183, 209, 0.5));
            }
        }

        /* Laser lines around text */
        .start-game-text::before {
            content: '';
            position: absolute;
            top: -20px;
            left: -20px;
            right: -20px;
            bottom: -20px;
            background: linear-gradient(45deg, transparent, #ff6b9d, transparent, #4ecdc4, transparent);
            background-size: 400% 400%;
            border-radius: 10px;
            opacity: 0;
            animation: laser-border 2s linear infinite;
            z-index: -1;
        }

        .start-game-text:hover::before {
            opacity: 0.7;
        }

        @keyframes laser-border {
            0%, 100% {
                background-position: 0% 50%;
                transform: rotate(0deg);
            }
            50% {
                background-position: 100% 50%;
                transform: rotate(180deg);
            }
        }

        /* Game instruction text */
        .game-instruction {
            font-family: 'Poppins', sans-serif;
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
            text-align: center;
            opacity: 0;
            margin-top: 70px;
        }

        @keyframes float-up {
            0%, 100% {
                transform: translateY(0px);
                opacity: 0.7;
            }
            50% {
                transform: translateY(-10px);
                opacity: 1;
            }
        }

        /* Enhanced Story Loading Animation */
        .story-loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at center, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
            z-index: 3000;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            opacity: 0;
            pointer-events: none;
        }

        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 30px;
        }

        /* Hexagonal Loading Spinner */
        .story-loading-spinner {
            width: 80px;
            height: 80px;
            position: relative;
        }

        .hexagon {
            width: 80px;
            height: 80px;
            position: absolute;
            border: 3px solid transparent;
            border-top: 3px solid #ff6b9d;
            border-right: 3px solid #4ecdc4;
            border-bottom: 3px solid #45b7d1;
            border-radius: 50%;
            animation: hexagon-spin 2s linear infinite;
        }

        .hexagon:nth-child(2) {
            width: 60px;
            height: 60px;
            top: 10px;
            left: 10px;
            border-top: 3px solid #4ecdc4;
            border-right: 3px solid #45b7d1;
            border-bottom: 3px solid #ff6b9d;
            animation: hexagon-spin 1.5s linear infinite reverse;
        }

        .hexagon:nth-child(3) {
            width: 40px;
            height: 40px;
            top: 20px;
            left: 20px;
            border-top: 3px solid #45b7d1;
            border-right: 3px solid #ff6b9d;
            border-bottom: 3px solid #4ecdc4;
            animation: hexagon-spin 1s linear infinite;
        }

        @keyframes hexagon-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Loading Progress Bar */
        .loading-progress {
            width: 200px;
            height: 4px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
            overflow: hidden;
            position: relative;
        }

        .loading-progress::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, #ff6b9d, #4ecdc4, #45b7d1);
            animation: loading-progress 2s ease-in-out infinite;
        }

        @keyframes loading-progress {
            0% {
                left: -100%;
            }
            100% {
                left: 100%;
            }
        }

        /* Loading Text with Typewriter Effect */
        .story-loading-text {
            font-family: 'Orbitron', monospace;
            font-size: 1.2rem;
            color: #fff;
            font-weight: 600;
            text-align: center;
            position: relative;
        }

        .loading-dots {
            display: inline-block;
            animation: loading-dots 1.5s ease-in-out infinite;
        }

        @keyframes loading-dots {
            0%, 20% {
                color: rgba(255, 255, 255, 0.4);
                text-shadow: none;
            }
            50% {
                color: #ff6b9d;
                text-shadow: 0 0 10px #ff6b9d;
            }
            100% {
                color: rgba(255, 255, 255, 0.4);
                text-shadow: none;
            }
        }

        /* Floating Particles in Loading */
        .loading-particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #ff6b9d;
            border-radius: 50%;
            animation: float-particle 3s ease-in-out infinite;
        }

        @keyframes float-particle {
            0%, 100% {
                transform: translateY(0px) scale(1);
                opacity: 0.7;
            }
            50% {
                transform: translateY(-30px) scale(1.2);
                opacity: 1;
            }
        }

        /* Floating Elements */
        .floating-element {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            bottom: 30%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        /* Comprehensive Responsive Design for All Devices */

        /* Large Desktop (1920px+) */
        @media (min-width: 1920px) {
            .game-container {
                max-width: 600px;
                height: 90vh;
            }

            .game-logo {
                font-size: 4rem;
            }

            .game-subtitle {
                font-size: 1.8rem;
            }

            .start-game-text {
                font-size: 4rem;
                letter-spacing: 8px;
                padding: 30px 0;
            }

            .game-menu {
                padding: 0 40px 100px;
                min-height: 250px;
            }
        }

        /* Desktop (1200px - 1919px) */
        @media (min-width: 1200px) and (max-width: 1919px) {
            .game-container {
                max-width: 550px;
                height: 85vh;
            }

            .game-logo {
                font-size: 3.5rem;
            }

            .game-subtitle {
                font-size: 1.6rem;
            }

            .start-game-text {
                font-size: 3.5rem;
                letter-spacing: 7px;
                padding: 25px 0;
            }

            .game-menu {
                padding: 0 35px 90px;
                min-height: 230px;
            }
        }

        /* Laptop (992px - 1199px) */
        @media (min-width: 992px) and (max-width: 1199px) {
            .game-container {
                max-width: 520px;
                height: 80vh;
            }

            .game-logo {
                font-size: 3rem;
            }

            .game-subtitle {
                font-size: 1.4rem;
            }

            .start-game-text {
                font-size: 3rem;
                letter-spacing: 6px;
                padding: 22px 0;
            }

            .game-menu {
                padding: 0 30px 80px;
                min-height: 210px;
            }
        }

        /* Tablet Landscape (768px - 991px) */
        @media (min-width: 768px) and (max-width: 991px) {
            .game-container {
                max-width: 480px;
                height: 75vh;
            }

            .game-logo {
                font-size: 2.8rem;
            }

            .game-subtitle {
                font-size: 1.3rem;
            }

            .start-game-text {
                font-size: 2.8rem;
                letter-spacing: 5px;
                padding: 20px 0;
            }

            .game-menu {
                padding: 0 25px 70px;
                min-height: 190px;
            }
        }

        /* Tablet Portrait (481px - 767px) */
        @media (min-width: 481px) and (max-width: 767px) {
            .game-container {
                max-width: 100%;
                height: 100vh;
                border-radius: 0;
            }

            .game-logo {
                font-size: 2.5rem;
            }

            .game-subtitle {
                font-size: 1.2rem;
            }

            .start-game-text {
                font-size: 2.5rem;
                letter-spacing: 4px;
                padding: 18px 0;
            }

            .game-menu {
                padding: 0 20px 60px;
                min-height: 170px;
            }
        }

        /* Large Phone (376px - 480px) */
        @media (min-width: 376px) and (max-width: 480px) {
            .game-container {
                max-width: 100%;
                height: 100vh;
                border-radius: 0;
            }

            .game-logo {
                font-size: 2.2rem;
            }

            .game-subtitle {
                font-size: 1.1rem;
            }

            .start-game-text {
                font-size: 2.2rem;
                letter-spacing: 3px;
                padding: 16px 0;
            }

            .game-menu {
                padding: 0 20px 55px;
                min-height: 150px;
            }

            .game-instruction {
                font-size: 0.85rem;
            }
        }

        /* Standard Phone (321px - 375px) */
        @media (min-width: 321px) and (max-width: 375px) {
            .header {
                padding: 40px 15px 20px;
            }

            .game-menu {
                padding: 0 15px 50px;
                min-height: 140px;
            }

            .game-logo {
                font-size: 2rem;
            }

            .game-subtitle {
                font-size: 1rem;
            }

            .start-game-text {
                font-size: 2rem;
                letter-spacing: 2px;
                padding: 14px 0;
            }

            .game-instruction {
                font-size: 0.8rem;
            }

            .story-loading-spinner {
                width: 70px;
                height: 70px;
            }

            .hexagon {
                width: 70px;
                height: 70px;
            }

            .hexagon:nth-child(2) {
                width: 52px;
                height: 52px;
                top: 9px;
                left: 9px;
            }

            .hexagon:nth-child(3) {
                width: 35px;
                height: 35px;
                top: 17.5px;
                left: 17.5px;
            }
        }

        /* Small Phone (320px and below) */
        @media (max-width: 320px) {
            .header {
                padding: 35px 10px 15px;
            }

            .game-menu {
                padding: 0 10px 45px;
                min-height: 130px;
            }

            .game-logo {
                font-size: 1.8rem;
            }

            .game-subtitle {
                font-size: 0.9rem;
            }

            .start-game-text {
                font-size: 1.8rem;
                letter-spacing: 1px;
                padding: 12px 0;
            }

            .game-instruction {
                font-size: 0.75rem;
            }

            .story-loading-spinner {
                width: 60px;
                height: 60px;
            }

            .hexagon {
                width: 60px;
                height: 60px;
            }

            .hexagon:nth-child(2) {
                width: 45px;
                height: 45px;
                top: 7.5px;
                left: 7.5px;
            }

            .hexagon:nth-child(3) {
                width: 30px;
                height: 30px;
                top: 15px;
                left: 15px;
            }
        }

        /* Landscape Orientation Support */
        @media (orientation: landscape) and (max-height: 600px) {
            .game-container {
                height: 100vh;
                max-height: none;
            }

            .header {
                padding: 20px 20px 10px;
            }

            .game-logo {
                font-size: 2rem;
            }

            .game-subtitle {
                font-size: 1rem;
            }

            .start-game-text {
                font-size: 2rem;
                letter-spacing: 3px;
                padding: 10px 0;
            }

            .game-menu {
                padding: 0 20px 40px;
                min-height: 100px;
            }

            .start-game-container {
                min-height: 80px;
                gap: 15px;
            }
        }

        /* Ultra-wide screens */
        @media (min-aspect-ratio: 21/9) {
            .game-container {
                max-width: 500px;
            }
        }

        /* High DPI displays */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            .start-game-text {
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }

            .game-logo, .game-subtitle {
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }
        }

        /* Safe area for notched devices */
        @supports (padding: max(0px)) {
            .header {
                padding-top: max(60px, env(safe-area-inset-top) + 20px);
            }

            .game-menu {
                padding-bottom: max(80px, env(safe-area-inset-bottom) + 40px);
            }
        }

        /* Foldable devices */
        @media (min-width: 768px) and (max-width: 1024px) and (orientation: portrait) {
            .game-container {
                max-width: 90%;
                height: 85vh;
            }
        }

        /* Touch device optimizations */
        @media (pointer: coarse) {
            .start-game-text {
                min-height: 60px;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px 10px;
            }

            .start-game-container {
                min-height: 140px;
            }
        }

        /* Hover capability detection */
        @media (hover: hover) and (pointer: fine) {
            .start-game-text:hover {
                transform: translateY(-15px) scale(1.15);
                letter-spacing: 8px;
            }
        }

        /* Reduced motion preferences */
        @media (prefers-reduced-motion: reduce) {
            .start-game-text,
            .game-logo,
            .floating-element,
            .star,
            .laser-line,
            .lightning {
                animation: none !important;
            }

            .start-game-text {
                background: #ff6b9d;
                -webkit-background-clip: unset;
                -webkit-text-fill-color: unset;
                color: #fff;
            }
        }

        /* Print styles */
        @media print {
            .game-container {
                background: white !important;
                color: black !important;
                box-shadow: none !important;
            }

            .start-game-text {
                background: none !important;
                color: black !important;
                -webkit-text-fill-color: black !important;
            }

            .stars-layer,
            .laser-grid,
            .lightning-layer,
            #particles-js {
                display: none !important;
            }
        }

        /* Sparkle effect */
        .sparkle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #fff;
            border-radius: 50%;
            animation: sparkle 1.5s ease-out forwards;
            pointer-events: none;
        }

        @keyframes sparkle {
            0% {
                opacity: 1;
                transform: scale(0) rotate(0deg);
            }
            50% {
                opacity: 1;
                transform: scale(1) rotate(180deg);
            }
            100% {
                opacity: 0;
                transform: scale(0) rotate(360deg);
            }
        }

        /* Hidden class for animations */
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <!-- Particles Background -->
    <div id="particles-js"></div>

    <!-- Enhanced Story Loading Screen -->
    <div class="story-loading-overlay" id="storyLoadingScreen">
        <div class="loading-container">
            <div class="story-loading-spinner">
                <div class="hexagon"></div>
                <div class="hexagon"></div>
                <div class="hexagon"></div>
            </div>
            <div class="loading-progress"></div>
            <div class="story-loading-text">
                Memasuki Dunia Virtual<span class="loading-dots">...</span>
            </div>
            <!-- Loading Particles -->
            <div class="loading-particle" style="top: 20%; left: 20%; animation-delay: 0s;"></div>
            <div class="loading-particle" style="top: 30%; right: 25%; animation-delay: 0.5s;"></div>
            <div class="loading-particle" style="bottom: 40%; left: 30%; animation-delay: 1s;"></div>
            <div class="loading-particle" style="bottom: 25%; right: 20%; animation-delay: 1.5s;"></div>
        </div>
    </div>

    <!-- Main Game Container -->
    <div class="game-container">
        <!-- Background Layers -->
        <div class="bg-overlay"></div>
        <div class="bg-gradient"></div>

        <!-- Animated Stars Layer -->
        <div class="stars-layer" id="starsLayer"></div>

        <!-- Laser Grid Animation -->
        <div class="laser-grid" id="laserGrid">
            <div class="laser-line" style="top: 20%; animation-delay: 0s;"></div>
            <div class="laser-line" style="top: 60%; animation-delay: 2s;"></div>
            <div class="laser-line vertical" style="left: 30%; animation-delay: 1s;"></div>
            <div class="laser-line vertical" style="right: 25%; animation-delay: 3s;"></div>
        </div>

        <!-- Lightning Effects -->
        <div class="lightning-layer" id="lightningLayer"></div>

        <!-- Floating Decorative Elements -->
        <div class="floating-element">💖</div>
        <div class="floating-element">✨</div>
        <div class="floating-element">🌸</div>

        <!-- Header Section -->
        <div class="header">
            <h1 class="game-logo" id="gameTitle">VIRTUAL</h1>
            <h2 class="game-subtitle" id="gameSubtitle">TEMAN WANITA</h2>
        </div>

        <!-- Game Menu - Bottom positioned -->
        <div class="game-menu" id="gameMenu">
            <div class="start-game-container">
                <div class="start-game-text" id="startGameText" onclick="startGame()">
                    START GAME
                </div>
                <div class="game-instruction" id="gameInstruction">
                    Tap to begin your virtual adventure
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize particles.js
        particlesJS('particles-js', {
            particles: {
                number: {
                    value: 50,
                    density: {
                        enable: true,
                        value_area: 800
                    }
                },
                color: {
                    value: ['#ff6b9d', '#4ecdc4', '#45b7d1']
                },
                shape: {
                    type: 'circle',
                    stroke: {
                        width: 0,
                        color: '#000000'
                    }
                },
                opacity: {
                    value: 0.3,
                    random: true,
                    anim: {
                        enable: true,
                        speed: 1,
                        opacity_min: 0.1,
                        sync: false
                    }
                },
                size: {
                    value: 3,
                    random: true,
                    anim: {
                        enable: true,
                        speed: 2,
                        size_min: 0.1,
                        sync: false
                    }
                },
                line_linked: {
                    enable: true,
                    distance: 150,
                    color: '#ff6b9d',
                    opacity: 0.2,
                    width: 1
                },
                move: {
                    enable: true,
                    speed: 1,
                    direction: 'none',
                    random: false,
                    straight: false,
                    out_mode: 'out',
                    bounce: false,
                    attract: {
                        enable: false,
                        rotateX: 600,
                        rotateY: 1200
                    }
                }
            },
            interactivity: {
                detect_on: 'canvas',
                events: {
                    onhover: {
                        enable: true,
                        mode: 'repulse'
                    },
                    onclick: {
                        enable: true,
                        mode: 'push'
                    },
                    resize: true
                },
                modes: {
                    grab: {
                        distance: 140,
                        line_linked: {
                            opacity: 1
                        }
                    },
                    bubble: {
                        distance: 400,
                        size: 40,
                        duration: 2,
                        opacity: 8,
                        speed: 3
                    },
                    repulse: {
                        distance: 100,
                        duration: 0.4
                    },
                    push: {
                        particles_nb: 4
                    },
                    remove: {
                        particles_nb: 2
                    }
                }
            },
            retina_detect: true
        });

        // Enhanced GSAP Animations
        document.addEventListener('DOMContentLoaded', function() {
            // Initial setup - hide elements
            gsap.set(['.game-logo', '.game-subtitle', '.start-game-text'], {
                opacity: 0
            });

            // Create animated background elements
            createAnimatedStars();
            createLightningEffects();

            // Start intro animation immediately
            startIntroAnimation();
        });

        function createAnimatedStars() {
            const starsLayer = document.getElementById('starsLayer');
            // Responsive star count based on screen size
            let starCount = 50;
            if (window.innerWidth >= 1920) starCount = 80;
            else if (window.innerWidth >= 1200) starCount = 70;
            else if (window.innerWidth >= 768) starCount = 60;
            else if (window.innerWidth <= 480) starCount = 30;

            for (let i = 0; i < starCount; i++) {
                const star = document.createElement('div');
                star.className = `star ${['small', 'medium', 'large'][Math.floor(Math.random() * 3)]}`;
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';
                star.style.animationDelay = Math.random() * 3 + 's';
                starsLayer.appendChild(star);
            }
        }

        function createLightningEffects() {
            const lightningLayer = document.getElementById('lightningLayer');
            // Responsive lightning count and size
            let lightningCount = 5;
            let maxHeight = 200;

            if (window.innerWidth >= 1920) {
                lightningCount = 8;
                maxHeight = 300;
            } else if (window.innerWidth >= 1200) {
                lightningCount = 7;
                maxHeight = 250;
            } else if (window.innerWidth <= 480) {
                lightningCount = 3;
                maxHeight = 150;
            }

            for (let i = 0; i < lightningCount; i++) {
                const lightning = document.createElement('div');
                lightning.className = 'lightning';
                lightning.style.left = Math.random() * 100 + '%';
                lightning.style.height = (Math.random() * maxHeight + 100) + 'px';
                lightning.style.top = Math.random() * 50 + '%';
                lightning.style.animationDelay = Math.random() * 5 + 's';
                lightningLayer.appendChild(lightning);
            }
        }

        function startIntroAnimation() {
            const tl = gsap.timeline();

            // Title animation with enhanced effects
            tl.to('.game-logo', {
                opacity: 1,
                y: 0,
                duration: 1.2,
                ease: 'back.out(1.7)'
            })
            .to('.game-subtitle', {
                opacity: 1,
                y: 0,
                duration: 1,
                ease: 'power2.out'
            }, '-=0.6')
            .to('.start-game-text', {
                opacity: 1,
                y: 0,
                duration: 1,
                ease: 'elastic.out(1, 0.5)'
            }, '-=0.4')
            .to('.game-instruction', {
                opacity: 1,
                y: 0,
                duration: 0.8,
                ease: 'power2.out'
            }, '-=0.2');

            // Enhanced glow effect for title
            gsap.to('.game-logo', {
                textShadow: '0 0 40px rgba(255, 107, 157, 1), 0 0 80px rgba(78, 205, 196, 0.5)',
                duration: 3,
                repeat: -1,
                yoyo: true,
                ease: 'power2.inOut'
            });

            // Floating animation for start game text
            gsap.to('.start-game-text', {
                y: -5,
                duration: 2,
                repeat: -1,
                yoyo: true,
                ease: 'power2.inOut'
            });
        }

        // Game Functions
        function startGame() {
            // Enhanced click animation with multiple effects
            animateTextClick(event.target);

            // Create explosion effect
            createExplosionEffect(event.target);

            // Show enhanced loading and navigate to story
            showLoadingTransition(() => {
                window.location.href = 'homepage.php';
            });
        }

        // Enhanced Utility Functions
        function animateTextClick(textElement) {
            // Enhanced scale animation with rotation
            gsap.to(textElement, {
                scale: 0.9,
                rotation: 2,
                duration: 0.15,
                yoyo: true,
                repeat: 1,
                ease: 'power2.inOut'
            });

            // Create multiple sparkle waves
            for (let wave = 0; wave < 3; wave++) {
                setTimeout(() => {
                    for (let i = 0; i < 12; i++) {
                        setTimeout(() => {
                            createSparkle(textElement);
                        }, i * 30);
                    }
                }, wave * 200);
            }

            // Enhanced glow effect with color cycling
            gsap.to(textElement, {
                filter: 'drop-shadow(0 0 30px #ff6b9d) drop-shadow(0 0 60px #4ecdc4) drop-shadow(0 0 90px #45b7d1)',
                duration: 0.4,
                yoyo: true,
                repeat: 1,
                ease: 'power2.inOut'
            });
        }

        function createSparkle(element) {
            const sparkle = document.createElement('div');
            sparkle.className = 'sparkle';

            const rect = element.getBoundingClientRect();
            const x = Math.random() * rect.width;
            const y = Math.random() * rect.height;

            sparkle.style.left = x + 'px';
            sparkle.style.top = y + 'px';
            sparkle.style.background = `hsl(${Math.random() * 60 + 300}, 100%, 80%)`;
            sparkle.style.boxShadow = `0 0 6px hsl(${Math.random() * 60 + 300}, 100%, 80%)`;

            element.appendChild(sparkle);

            setTimeout(() => {
                if (sparkle.parentNode) {
                    sparkle.parentNode.removeChild(sparkle);
                }
            }, 1500);
        }

        function createExplosionEffect(element) {
            const rect = element.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;

            // Create explosion particles
            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.style.cssText = `
                    position: fixed;
                    left: ${centerX}px;
                    top: ${centerY}px;
                    width: 4px;
                    height: 4px;
                    background: hsl(${Math.random() * 60 + 300}, 100%, 70%);
                    border-radius: 50%;
                    pointer-events: none;
                    z-index: 1000;
                `;

                document.body.appendChild(particle);

                const angle = (i / 20) * Math.PI * 2;
                const distance = 100 + Math.random() * 50;
                const endX = centerX + Math.cos(angle) * distance;
                const endY = centerY + Math.sin(angle) * distance;

                gsap.to(particle, {
                    x: endX - centerX,
                    y: endY - centerY,
                    scale: 0,
                    duration: 0.8,
                    ease: 'power2.out',
                    onComplete: () => {
                        if (particle.parentNode) {
                            particle.parentNode.removeChild(particle);
                        }
                    }
                });
            }
        }

        function showLoadingTransition(callback) {
            const loadingScreen = document.getElementById('storyLoadingScreen');
            loadingScreen.style.pointerEvents = 'all';

            // Animate loading screen entrance
            gsap.to(loadingScreen, {
                opacity: 1,
                duration: 0.6,
                ease: 'power2.out'
            });

            // Animate loading elements
            gsap.fromTo('.story-loading-spinner',
                { scale: 0, rotation: 0 },
                { scale: 1, rotation: 360, duration: 0.8, ease: 'back.out(1.7)', delay: 0.2 }
            );

            gsap.fromTo('.loading-progress',
                { scaleX: 0 },
                { scaleX: 1, duration: 0.6, ease: 'power2.out', delay: 0.4 }
            );

            gsap.fromTo('.story-loading-text',
                { y: 30, opacity: 0 },
                { y: 0, opacity: 1, duration: 0.5, ease: 'power2.out', delay: 0.6 }
            );

            // Animate loading particles
            gsap.fromTo('.loading-particle',
                { scale: 0, opacity: 0 },
                { scale: 1, opacity: 1, duration: 0.4, stagger: 0.1, delay: 0.8 }
            );

            // Navigate after animations complete
            setTimeout(callback, 2500);
        }

        // Add touch feedback for mobile
        document.getElementById('startGameText').addEventListener('touchstart', function() {
            gsap.to(this, {
                scale: 0.95,
                duration: 0.1
            });
        });

        document.getElementById('startGameText').addEventListener('touchend', function() {
            gsap.to(this, {
                scale: 1,
                duration: 0.1
            });
        });

        // Preload story page for faster navigation
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = 'story.php';
        document.head.appendChild(link);

        // Add mouse trail effect for desktop
        document.addEventListener('mousemove', function(e) {
            if (Math.random() < 0.1) { // Only create particles occasionally
                createMouseTrail(e.clientX, e.clientY);
            }
        });

        function createMouseTrail(x, y) {
            const trail = document.createElement('div');
            trail.style.cssText = `
                position: fixed;
                left: ${x}px;
                top: ${y}px;
                width: 3px;
                height: 3px;
                background: radial-gradient(circle, #ff6b9d, transparent);
                border-radius: 50%;
                pointer-events: none;
                z-index: 1;
                animation: particle-fade 1s ease-out forwards;
            `;

            document.body.appendChild(trail);

            setTimeout(() => {
                if (trail.parentNode) {
                    trail.parentNode.removeChild(trail);
                }
            }, 1000);
        }

        // Add particle trail animation CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes particle-fade {
                0% {
                    opacity: 1;
                    transform: scale(1);
                }
                100% {
                    opacity: 0;
                    transform: scale(0) translateY(-30px);
                }
            }
        `;
        document.head.appendChild(style);

        // Responsive handler for window resize
        let resizeTimeout;
        window.addEventListener('resize', function() {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(function() {
                // Recreate background elements with new responsive values
                const starsLayer = document.getElementById('starsLayer');
                const lightningLayer = document.getElementById('lightningLayer');

                // Clear existing elements
                starsLayer.innerHTML = '';
                lightningLayer.innerHTML = '';

                // Recreate with responsive counts
                createAnimatedStars();
                createLightningEffects();

                // Adjust particles.js for new screen size
                if (window.pJSDom && window.pJSDom[0] && window.pJSDom[0].pJS) {
                    const pJS = window.pJSDom[0].pJS;
                    let particleCount = 50;

                    if (window.innerWidth >= 1920) particleCount = 80;
                    else if (window.innerWidth >= 1200) particleCount = 70;
                    else if (window.innerWidth >= 768) particleCount = 60;
                    else if (window.innerWidth <= 480) particleCount = 30;

                    pJS.particles.number.value = particleCount;
                    pJS.fn.particlesRefresh();
                }
            }, 250);
        });

        // Device orientation change handler
        window.addEventListener('orientationchange', function() {
            setTimeout(function() {
                // Trigger resize handler after orientation change
                window.dispatchEvent(new Event('resize'));
            }, 100);
        });

        // Performance optimization for low-end devices
        function optimizeForDevice() {
            const isLowEnd = navigator.hardwareConcurrency <= 2 ||
                           navigator.deviceMemory <= 2 ||
                           /Android.*Chrome\/[0-5]/.test(navigator.userAgent);

            if (isLowEnd) {
                // Reduce particle count
                const starsLayer = document.getElementById('starsLayer');
                const stars = starsLayer.querySelectorAll('.star');
                for (let i = stars.length - 1; i >= stars.length / 2; i--) {
                    stars[i].remove();
                }

                // Reduce lightning effects
                const lightningLayer = document.getElementById('lightningLayer');
                const lightning = lightningLayer.querySelectorAll('.lightning');
                for (let i = lightning.length - 1; i >= lightning.length / 2; i--) {
                    lightning[i].remove();
                }

                // Reduce laser lines
                const laserLines = document.querySelectorAll('.laser-line');
                for (let i = laserLines.length - 1; i >= 2; i--) {
                    laserLines[i].remove();
                }
            }
        }

        // Apply optimizations after page load
        window.addEventListener('load', optimizeForDevice);
    </script>
</body>
</html>