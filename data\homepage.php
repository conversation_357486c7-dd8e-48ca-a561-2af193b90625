<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Game Virtual - Homepage</title>
    <link rel="stylesheet" href="../css/homepage.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Status Bar (Mobile Only) -->
    <div class="status-bar">
        <div class="time">03:02 AM</div>
        <div class="battery-info">
            <div class="wifi-icon"></div>
            <span>60%</span>
            <div class="battery-icon"></div>
        </div>
    </div>

    <div class="container">
        <!-- Store Button -->
        <button class="store-btn" onclick="openStore()">
            🛒 Store
        </button>

        <!-- Character Showcase -->
        <div class="character-showcase">
            <img src="https://via.placeholder.com/400x400/ff6b6b/ffffff?text=Character+1" alt="Game Character" class="character-image" id="characterImage">
            <div class="carousel-dots">
                <div class="dot active" onclick="changeCharacter(0)"></div>
                <div class="dot" onclick="changeCharacter(1)"></div>
                <div class="dot" onclick="changeCharacter(2)"></div>
                <div class="dot" onclick="changeCharacter(3)"></div>
            </div>
        </div>

        <!-- Game Cards Section -->
        <div class="game-cards">
            <div class="game-card" onclick="openSection('Gallery')">
                <h3>🎮 Gallery</h3>
                <p>View character collection</p>
            </div>
            <div class="game-card" onclick="openSection('Story')">
                <h3>📖 Story</h3>
                <p>Continue your adventure</p>
                <div class="notification-badge">!</div>
            </div>
            <div class="game-card large" onclick="openSection('Stage')">
                <h3>🎯 Stage</h3>
                <p>Battle challenges await</p>
                <div class="notification-badge">!</div>
            </div>
        </div>

        <!-- Navigation Grid -->
        <div class="nav-grid">
            <a href="#" class="nav-item" onclick="openFeature('Palrang Talk')">
                <div class="nav-icon red">💬</div>
                <div class="nav-label">Palrang Talk</div>
                <div class="notification-badge">6</div>
            </a>
            <a href="#" class="nav-item" onclick="openFeature('Palrang Plus')">
                <div class="nav-icon pink">💝</div>
                <div class="nav-label">Palrang Plus</div>
            </a>
            <a href="#" class="nav-item" onclick="openFeature('Vanguard Squad')">
                <div class="nav-icon blue">🛡️</div>
                <div class="nav-label">Vanguard Squad</div>
            </a>
            <a href="#" class="nav-item" onclick="openFeature('Vanguards')">
                <div class="nav-icon purple">👥</div>
                <div class="nav-label">Vanguards</div>
            </a>
            <a href="#" class="nav-item" onclick="openFeature('Aide')">
                <div class="nav-icon green">👤</div>
                <div class="nav-label">Aide</div>
            </a>
            <a href="#" class="nav-item" onclick="openFeature('Encyclopedia')">
                <div class="nav-icon gray">📚</div>
                <div class="nav-label">Encyclopedia</div>
                <div class="notification-badge">!</div>
            </a>
            <a href="#" class="nav-item" onclick="openFeature('Checklist')">
                <div class="nav-icon blue">📋</div>
                <div class="nav-label">Checklist</div>
            </a>
            <a href="#" class="nav-item" onclick="openFeature('Bag')">
                <div class="nav-icon orange">🎒</div>
                <div class="nav-label">Bag</div>
            </a>
        </div>
    </div>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <a href="#" class="bottom-nav-item" onclick="openBottomFeature('Phone')">
            <div class="bottom-nav-icon green">📞</div>
        </a>
        <a href="#" class="bottom-nav-item" onclick="openBottomFeature('Announcements')">
            <div class="bottom-nav-icon gray">📢</div>
        </a>
        <a href="#" class="bottom-nav-item" onclick="openBottomFeature('Mail')">
            <div class="bottom-nav-icon blue">✉️</div>
        </a>
        <a href="#" class="bottom-nav-item" onclick="openBottomFeature('Settings')">
            <div class="bottom-nav-icon dark">⚙️</div>
        </a>
    </div>

    <script>
        // Character carousel data
        const characters = [
            {
                image: 'https://via.placeholder.com/400x400/ff6b6b/ffffff?text=Character+1',
                name: 'Warrior Princess'
            },
            {
                image: 'https://via.placeholder.com/400x400/4ecdc4/ffffff?text=Character+2',
                name: 'Mystic Mage'
            },
            {
                image: 'https://via.placeholder.com/400x400/45b7d1/ffffff?text=Character+3',
                name: 'Shadow Assassin'
            },
            {
                image: 'https://via.placeholder.com/400x400/96ceb4/ffffff?text=Character+4',
                name: 'Divine Healer'
            }
        ];

        let currentCharacterIndex = 0;

        // Character carousel functionality
        function changeCharacter(index) {
            const dots = document.querySelectorAll('.dot');
            const characterImage = document.getElementById('characterImage');
            
            // Remove active class from all dots
            dots.forEach(dot => dot.classList.remove('active'));
            
            // Add active class to selected dot
            dots[index].classList.add('active');
            
            // Change character image
            characterImage.src = characters[index].image;
            characterImage.alt = characters[index].name;
            
            currentCharacterIndex = index;
        }

        // Auto-rotate carousel every 5 seconds
        setInterval(() => {
            currentCharacterIndex = (currentCharacterIndex + 1) % characters.length;
            changeCharacter(currentCharacterIndex);
        }, 5000);

        // Game section functions
        function openStore() {
            alert('🛒 Opening Store...\nBrowse items, characters, and upgrades!');
        }

        function openSection(sectionName) {
            alert(`🎮 Opening ${sectionName}...\nPrepare for adventure!`);
        }

        function openFeature(featureName) {
            alert(`✨ Opening ${featureName}...\nExplore new features!`);
        }

        function openBottomFeature(featureName) {
            alert(`🔧 Opening ${featureName}...\nAccess your tools!`);
        }

        // Add loading animation
        window.addEventListener('load', () => {
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.5s ease';
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);
        });

        // Add touch support for mobile
        let touchStartX = 0;
        let touchEndX = 0;

        document.addEventListener('touchstart', e => {
            touchStartX = e.changedTouches[0].screenX;
        });

        document.addEventListener('touchend', e => {
            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        });

        function handleSwipe() {
            const swipeThreshold = 50;
            const diff = touchStartX - touchEndX;
            
            if (Math.abs(diff) > swipeThreshold) {
                if (diff > 0) {
                    // Swipe left - next character
                    const nextIndex = (currentCharacterIndex + 1) % characters.length;
                    changeCharacter(nextIndex);
                } else {
                    // Swipe right - previous character
                    const prevIndex = currentCharacterIndex === 0 ? characters.length - 1 : currentCharacterIndex - 1;
                    changeCharacter(prevIndex);
                }
            }
        }
    </script>
</body>
</html>
