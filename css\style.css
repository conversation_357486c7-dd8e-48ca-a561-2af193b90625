/* Basic styles for Virtual Teman Wanita */
:root {
  --bg: #0f1014;
  --card: #181a21;
  --muted: #9aa0a6;
  --primary: #6c5ce7;
  --accent: #00d1b2;
}
* { box-sizing: border-box; }
html, body { height: 100%; }
html { font-size: clamp(14px, 2.4vw, 16px); }

body {
  margin: 0;
  font-family: 'Poppins', system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif;
  background: radial-gradient(1200px 800px at 10% 20%, #1f2330, #0f1014 60%);
  color: #e7e9ee;
}
.site-header, .site-footer {
  display: flex; align-items: center; justify-content: space-between;
  padding: 12px 16px; background: rgba(0,0,0,.25);
  position: sticky; top: 0; backdrop-filter: blur(6px);
}
.site-footer { position: static; justify-content: center; color: var(--muted); font-size: 12px; padding: 20px; }
.title { font-weight: 700; letter-spacing: .5px; }
nav .btn, .btn { color: #fff; text-decoration: none; border: 1px solid rgba(255,255,255,.2); padding: 8px 12px; border-radius: 10px; display: inline-block; }
.btn.primary { background: linear-gradient(135deg, var(--primary), #8e7cff); border-color: transparent; }
.welcome { margin-right: 8px; color: var(--muted) }

.auth-section { display: grid; place-items: center; padding: 24px; }
.card { background: var(--card); border: 1px solid rgba(255,255,255,.06); border-radius: 16px; padding: 16px; width: 100%; max-width: 420px; }
.card h2 { margin: 0 0 8px; }
.card label { display: block; margin: 8px 0 6px; font-size: 13px; color: var(--muted); }
.card input { width: 100%; padding: 10px 12px; border-radius: 10px; border: 1px solid rgba(255,255,255,.1); background: #12141a; color: #fff; }
.card form { display: grid; gap: 10px; }
.card .muted { color: var(--muted); font-size: 12px; margin-top: 10px; }
.stack-sm { margin-top: 8px; }

/* Home vertical layout (inspired by screenshot) */
.home-vertical { display: grid; place-items: center; padding: 10px 10px 40px; }
.scene { width: 100%; max-width: 980px; aspect-ratio: 9/16; position: relative; background: url('/assets/images/bg-blur.jpg') center/cover no-repeat, linear-gradient(180deg, #1b1e27, #0f1014); border-radius: 28px; border: 1px solid rgba(255,255,255,.06); overflow: hidden; }

.character { position: absolute; left: 0; top: 0; bottom: 0; width: 46%; display: grid; place-items: end center; padding: 16px; }
.character-img { width: 95%; height: 97%; background: url('/assets/characters/nurse.png') bottom center/contain no-repeat, radial-gradient(500px 400px at 50% 60%, rgba(255,192,203,.15), rgba(255,255,255,0)); filter: drop-shadow(0 10px 25px rgba(0,0,0,.45)); border-radius: 24px; }

.phone-ui { position: absolute; right: 3%; top: 5%; bottom: 5%; width: 38%; background: rgba(10,10,14,.6); border: 1px solid rgba(255,255,255,.1); border-radius: 32px; padding: 12px; display: grid; grid-template-rows: auto auto auto 1fr; gap: 10px; backdrop-filter: blur(8px); }
.statusbar { display: flex; justify-content: space-between; font-size: 12px; color: #cfd3d8; }
.store-card { display: grid; grid-template-columns: 36% 1fr; gap: 10px; background: #0f1219; border: 1px solid rgba(255,255,255,.05); border-radius: 16px; padding: 8px; align-items: center; }
.store-card .thumb { aspect-ratio: 4/3; background: linear-gradient(135deg, #2f80ed, #b06ab3); border-radius: 12px; }
.store-card .meta .title { font-weight: 600; }
.store-card .meta .subtitle { color: var(--muted); font-size: 12px; }
.stage-card { background: #121520; border: 1px solid rgba(255,255,255,.05); border-radius: 16px; padding: 14px; font-weight: 600; text-align: center; }
.grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px; }
.app { background: #151823; border: 1px solid rgba(255,255,255,.06); color: #fff; border-radius: 18px; padding: 14px 6px; display: grid; place-items: center; gap: 6px; font-weight: 600; }
.app span { font-size: 12px; color: #e7e9ee; }
.app:hover { outline: 2px solid rgba(108,92,231,.5); cursor: pointer; }

.cta-overlay { margin-top: 8px; background: rgba(0,0,0,.35); border: 1px solid rgba(255,255,255,.06); border-radius: 12px; padding: 12px; text-align: center; }

/* Responsive tweaks */
@media (max-width: 900px) {
  .home-vertical { padding: 0; min-height: 100dvh; display: grid; place-items: center; }
  .scene { width: 100vw; max-width: 430px; height: 100dvh; aspect-ratio: auto; margin: 0 auto; border-radius: 0; background: url('/assets/images/bg-blur.jpg') center/cover no-repeat, linear-gradient(180deg, #1b1e27, #0f1014); }
  .character { display: none; }
  .phone-ui { position: relative; inset: unset; width: 100%; height: 100%; border-radius: 24px; padding: calc(12px + env(safe-area-inset-top)) 12px calc(16px + env(safe-area-inset-bottom)); overflow-y: auto; -webkit-overflow-scrolling: touch; }
  .statusbar { position: sticky; top: 0; background: rgba(10,10,14,.6); padding: 6px 8px; border-radius: 12px; z-index: 2; }
}

@media (max-width: 420px) {
  .grid { grid-template-columns: repeat(4, 1fr); }
}

@media (max-width: 360px) {
  .grid { grid-template-columns: repeat(3, 1fr); }
}
