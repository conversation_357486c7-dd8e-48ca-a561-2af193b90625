/* Additional CSS for enhanced effects */

/* Glitch effect for title */
@keyframes glitch {
    0% {
        text-shadow: 0.05em 0 0 #ff6b9d, -0.05em -0.025em 0 #4ecdc4, 0.025em 0.05em 0 #45b7d1;
    }
    15% {
        text-shadow: 0.05em 0 0 #ff6b9d, -0.05em -0.025em 0 #4ecdc4, 0.025em 0.05em 0 #45b7d1;
    }
    16% {
        text-shadow: -0.05em -0.025em 0 #ff6b9d, 0.025em 0.025em 0 #4ecdc4, -0.05em -0.05em 0 #45b7d1;
    }
    49% {
        text-shadow: -0.05em -0.025em 0 #ff6b9d, 0.025em 0.025em 0 #4ecdc4, -0.05em -0.05em 0 #45b7d1;
    }
    50% {
        text-shadow: 0.025em 0.05em 0 #ff6b9d, 0.05em 0 0 #4ecdc4, 0 -0.05em 0 #45b7d1;
    }
    99% {
        text-shadow: 0.025em 0.05em 0 #ff6b9d, 0.05em 0 0 #4ecdc4, 0 -0.05em 0 #45b7d1;
    }
    100% {
        text-shadow: -0.025em 0 0 #ff6b9d, -0.025em -0.025em 0 #4ecdc4, -0.025em -0.05em 0 #45b7d1;
    }
}

.game-logo:hover {
    animation: glitch 0.5s;
}

/* Neon border effect */
@keyframes neon-border {
    0%, 100% {
        box-shadow: 
            0 0 5px #ff6b9d,
            0 0 10px #ff6b9d,
            0 0 15px #ff6b9d,
            0 0 20px #ff6b9d;
    }
    50% {
        box-shadow: 
            0 0 10px #4ecdc4,
            0 0 20px #4ecdc4,
            0 0 30px #4ecdc4,
            0 0 40px #4ecdc4;
    }
}

.menu-button:focus {
    outline: none;
    animation: neon-border 2s infinite;
}

/* Holographic effect */
@keyframes holographic {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.character-image:hover {
    background: linear-gradient(45deg, #ff6b9d, #4ecdc4, #45b7d1, #ff6b9d);
    background-size: 400% 400%;
    animation: holographic 3s ease infinite;
    mix-blend-mode: overlay;
}

/* Particle trail effect */
.particle-trail {
    position: absolute;
    width: 4px;
    height: 4px;
    background: radial-gradient(circle, #ff6b9d, transparent);
    border-radius: 50%;
    pointer-events: none;
    animation: particle-fade 1s ease-out forwards;
}

@keyframes particle-fade {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    100% {
        opacity: 0;
        transform: scale(0) translateY(-50px);
    }
}

/* Cyberpunk grid background */
.cyber-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(rgba(255, 107, 157, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255, 107, 157, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: grid-move 20s linear infinite;
    z-index: 1;
}

@keyframes grid-move {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(50px, 50px);
    }
}

/* Scan line effect */
.scan-line {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #ff6b9d, transparent);
    animation: scan 3s linear infinite;
    z-index: 5;
}

@keyframes scan {
    0% {
        top: 0;
        opacity: 1;
    }
    100% {
        top: 100%;
        opacity: 0;
    }
}

/* Button hover glow */
.menu-button::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50px;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.menu-button:hover::after {
    opacity: 1;
}

/* Typewriter effect */
@keyframes typewriter {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

.typewriter {
    overflow: hidden;
    border-right: 2px solid #ff6b9d;
    white-space: nowrap;
    animation: 
        typewriter 2s steps(20, end),
        blink-caret 0.75s step-end infinite;
}

@keyframes blink-caret {
    from, to {
        border-color: transparent;
    }
    50% {
        border-color: #ff6b9d;
    }
}

/* Mobile optimizations */
@media (max-width: 480px) {
    .cyber-grid {
        background-size: 30px 30px;
    }
    
    .particle-trail {
        width: 3px;
        height: 3px;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .menu-button {
        border: 2px solid #ffffff;
    }
    
    .game-logo {
        text-shadow: 2px 2px 4px #000000;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    .floating-element,
    .character-image,
    .game-logo {
        animation: none !important;
    }
    
    .menu-button {
        transition: none !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --primary-color: #ff8cc8;
        --secondary-color: #6ee7e0;
        --accent-color: #5bc7f0;
    }
}

/* Print styles */
@media print {
    .game-container {
        background: white !important;
        color: black !important;
    }
    
    .menu-button {
        background: #cccccc !important;
        color: black !important;
    }
}
