/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: white;
    overflow-x: hidden;
    min-height: 100vh;
}

.container {
    max-width: 100vw;
    min-height: 100vh;
    position: relative;
    padding: 20px 15px;
}

/* Status Bar */
.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: bold;
}

.time {
    color: #fff;
}

.battery-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.wifi-icon, .battery-icon {
    width: 20px;
    height: 15px;
    background: #fff;
    border-radius: 2px;
}

/* Store Button */
.store-btn {
    position: absolute;
    top: 60px;
    left: 20px;
    background: rgba(0, 0, 0, 0.7);
    border: none;
    border-radius: 15px;
    padding: 10px 15px;
    color: white;
    font-size: 16px;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 8px;
    z-index: 10;
    cursor: pointer;
    transition: all 0.3s ease;
}

.store-btn:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.05);
}

/* Character Showcase */
.character-showcase {
    position: relative;
    height: 400px;
    margin: 80px 0 30px 0;
    border-radius: 20px;
    overflow: hidden;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
}

.character-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 20px;
}

.carousel-dots {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active {
    background: #00ffff;
    transform: scale(1.2);
}

/* Game Cards Section */
.game-cards {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin: 30px 0;
}

.game-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.game-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.game-card.large {
    grid-column: span 2;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
    min-height: 100px;
}

.game-card h3 {
    font-size: 18px;
    margin-bottom: 8px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.game-card p {
    font-size: 12px;
    opacity: 0.9;
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

/* Navigation Grid */
.nav-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin: 30px 0;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: white;
    transition: all 0.3s ease;
    position: relative;
}

.nav-item:hover {
    transform: scale(1.1);
}

.nav-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-bottom: 8px;
    position: relative;
}

.nav-icon.red { background: linear-gradient(135deg, #ff6b6b, #ee5a52); }
.nav-icon.pink { background: linear-gradient(135deg, #ff9ff3, #f368e0); }
.nav-icon.blue { background: linear-gradient(135deg, #74b9ff, #0984e3); }
.nav-icon.purple { background: linear-gradient(135deg, #a29bfe, #6c5ce7); }
.nav-icon.green { background: linear-gradient(135deg, #55efc4, #00b894); }
.nav-icon.gray { background: linear-gradient(135deg, #636e72, #2d3436); }
.nav-icon.orange { background: linear-gradient(135deg, #fdcb6e, #e17055); }

.nav-label {
    font-size: 12px;
    text-align: center;
    font-weight: 500;
}

/* Bottom Navigation */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: space-around;
    padding: 15px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.bottom-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.bottom-nav-item:hover {
    color: #00ffff;
    transform: scale(1.1);
}

.bottom-nav-icon {
    width: 30px;
    height: 30px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    margin-bottom: 4px;
}

.bottom-nav-icon.green { background: #00d2d3; }
.bottom-nav-icon.gray { background: #636e72; }
.bottom-nav-icon.blue { background: #74b9ff; }
.bottom-nav-icon.dark { background: #2d3436; }

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.game-card, .nav-item {
    animation: fadeInUp 0.6s ease forwards;
}

.game-card:nth-child(2) { animation-delay: 0.1s; }
.game-card:nth-child(3) { animation-delay: 0.2s; }
.game-card:nth-child(4) { animation-delay: 0.3s; }

/* ===== RESPONSIVE DESIGN ===== */

/* Mobile Phones (320px - 480px) */
@media (max-width: 480px) {
    .container {
        padding: 15px 10px;
    }

    .status-bar {
        padding: 8px 15px;
        font-size: 13px;
    }

    .store-btn {
        top: 50px;
        left: 15px;
        padding: 8px 12px;
        font-size: 14px;
    }

    .character-showcase {
        height: 300px;
        margin: 60px 0 20px 0;
    }

    .game-cards {
        gap: 10px;
        margin: 20px 0;
    }

    .game-card {
        padding: 15px;
        min-height: 100px;
    }

    .game-card h3 {
        font-size: 16px;
    }

    .game-card p {
        font-size: 11px;
    }

    .nav-grid {
        gap: 15px;
        margin: 20px 0;
    }

    .nav-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .nav-label {
        font-size: 11px;
    }

    .bottom-nav {
        padding: 12px 0;
    }

    .bottom-nav-icon {
        width: 28px;
        height: 28px;
        font-size: 16px;
    }
}

/* Large Mobile Phones (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
    .container {
        padding: 20px;
        max-width: 600px;
        margin: 0 auto;
    }

    .character-showcase {
        height: 450px;
        margin: 90px 0 40px 0;
    }

    .game-cards {
        gap: 20px;
        margin: 40px 0;
    }

    .game-card {
        padding: 25px;
        min-height: 140px;
    }

    .game-card h3 {
        font-size: 20px;
    }

    .game-card p {
        font-size: 14px;
    }

    .nav-grid {
        gap: 25px;
        margin: 40px 0;
    }

    .nav-icon {
        width: 70px;
        height: 70px;
        font-size: 28px;
    }

    .nav-label {
        font-size: 13px;
    }
}

/* Tablets (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
    .container {
        max-width: 800px;
        margin: 0 auto;
        padding: 30px;
    }

    .status-bar {
        display: none; /* Hide mobile status bar on tablets */
    }

    .store-btn {
        top: 30px;
        left: 30px;
        padding: 12px 20px;
        font-size: 18px;
    }

    .character-showcase {
        height: 500px;
        margin: 60px 0 50px 0;
    }

    .game-cards {
        grid-template-columns: repeat(3, 1fr);
        gap: 25px;
        margin: 50px 0;
    }

    .game-card.large {
        grid-column: span 3;
    }

    .game-card {
        padding: 30px;
        min-height: 160px;
    }

    .game-card h3 {
        font-size: 22px;
    }

    .game-card p {
        font-size: 15px;
    }

    .nav-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 30px;
        margin: 50px 0;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .nav-icon {
        width: 80px;
        height: 80px;
        font-size: 32px;
    }

    .nav-label {
        font-size: 14px;
    }

    .bottom-nav {
        position: relative;
        background: rgba(0, 0, 0, 0.7);
        border-radius: 20px;
        margin: 30px;
        padding: 20px 0;
    }

    .bottom-nav-icon {
        width: 40px;
        height: 40px;
        font-size: 22px;
    }
}

/* Laptops and Small Desktops (1025px - 1440px) */
@media (min-width: 1025px) and (max-width: 1440px) {
    body {
        background-attachment: fixed;
    }

    .container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 40px;
    }

    .status-bar {
        display: none;
    }

    .store-btn {
        top: 40px;
        left: 40px;
        padding: 15px 25px;
        font-size: 20px;
    }

    .character-showcase {
        height: 600px;
        margin: 80px 0 60px 0;
    }

    .game-cards {
        grid-template-columns: repeat(4, 1fr);
        gap: 30px;
        margin: 60px 0;
    }

    .game-card.large {
        grid-column: span 2;
    }

    .game-card {
        padding: 35px;
        min-height: 180px;
    }

    .game-card h3 {
        font-size: 24px;
    }

    .game-card p {
        font-size: 16px;
    }

    .nav-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 40px;
        margin: 60px 0;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
    }

    .nav-icon {
        width: 90px;
        height: 90px;
        font-size: 36px;
    }

    .nav-label {
        font-size: 15px;
    }

    .bottom-nav {
        position: relative;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 25px;
        margin: 40px auto;
        padding: 25px 0;
        max-width: 600px;
    }

    .bottom-nav-icon {
        width: 50px;
        height: 50px;
        font-size: 26px;
    }
}

/* Large Desktops (1441px and above) */
@media (min-width: 1441px) {
    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 50px;
    }

    .store-btn {
        top: 50px;
        left: 50px;
        padding: 18px 30px;
        font-size: 22px;
    }

    .character-showcase {
        height: 700px;
        margin: 100px 0 80px 0;
    }

    .game-cards {
        grid-template-columns: repeat(4, 1fr);
        gap: 40px;
        margin: 80px 0;
    }

    .game-card {
        padding: 40px;
        min-height: 200px;
    }

    .game-card h3 {
        font-size: 26px;
    }

    .game-card p {
        font-size: 18px;
    }

    .nav-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 50px;
        margin: 80px 0;
        max-width: 1000px;
        margin-left: auto;
        margin-right: auto;
    }

    .nav-icon {
        width: 100px;
        height: 100px;
        font-size: 40px;
    }

    .nav-label {
        font-size: 16px;
    }

    .bottom-nav {
        max-width: 800px;
        padding: 30px 0;
        margin: 50px auto;
    }

    .bottom-nav-icon {
        width: 60px;
        height: 60px;
        font-size: 30px;
    }
}

/* Hover Effects for Desktop */
@media (min-width: 1025px) {
    .game-card:hover {
        transform: translateY(-10px) scale(1.02);
    }

    .nav-item:hover {
        transform: scale(1.15);
    }

    .store-btn:hover {
        transform: scale(1.1);
    }

    .bottom-nav-item:hover {
        transform: scale(1.2);
    }
}

/* Print Styles */
@media print {
    .bottom-nav,
    .store-btn,
    .status-bar {
        display: none;
    }

    body {
        background: white;
        color: black;
    }

    .container {
        padding: 20px;
    }
}
