<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Game Virtual - Homepage</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
            overflow-x: hidden;
            min-height: 100vh;
        }

        .container {
            max-width: 100vw;
            min-height: 100vh;
            position: relative;
            padding: 20px 15px;
        }

        /* Status Bar */
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            font-size: 14px;
            font-weight: bold;
        }

        .time {
            color: #fff;
        }

        .battery-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .wifi-icon, .battery-icon {
            width: 20px;
            height: 15px;
            background: #fff;
            border-radius: 2px;
        }

        /* Store Button */
        .store-btn {
            position: absolute;
            top: 60px;
            left: 20px;
            background: rgba(0, 0, 0, 0.7);
            border: none;
            border-radius: 15px;
            padding: 10px 15px;
            color: white;
            font-size: 16px;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 8px;
            z-index: 10;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .store-btn:hover {
            background: rgba(0, 0, 0, 0.9);
            transform: scale(1.05);
        }

        /* Character Showcase */
        .character-showcase {
            position: relative;
            height: 400px;
            margin: 80px 0 30px 0;
            border-radius: 20px;
            overflow: hidden;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
        }

        .character-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 20px;
        }

        .carousel-dots {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
        }

        .dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .dot.active {
            background: #00ffff;
            transform: scale(1.2);
        }

        /* Game Cards Section */
        .game-cards {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 30px 0;
        }

        .game-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            min-height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .game-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .game-card.large {
            grid-column: span 2;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            min-height: 100px;
        }

        .game-card h3 {
            font-size: 18px;
            margin-bottom: 8px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .game-card p {
            font-size: 12px;
            opacity: 0.9;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        /* Navigation Grid */
        .nav-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin: 30px 0;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: white;
            transition: all 0.3s ease;
        }

        .nav-item:hover {
            transform: scale(1.1);
        }

        .nav-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-bottom: 8px;
            position: relative;
        }

        .nav-icon.red { background: linear-gradient(135deg, #ff6b6b, #ee5a52); }
        .nav-icon.pink { background: linear-gradient(135deg, #ff9ff3, #f368e0); }
        .nav-icon.blue { background: linear-gradient(135deg, #74b9ff, #0984e3); }
        .nav-icon.purple { background: linear-gradient(135deg, #a29bfe, #6c5ce7); }
        .nav-icon.green { background: linear-gradient(135deg, #55efc4, #00b894); }
        .nav-icon.gray { background: linear-gradient(135deg, #636e72, #2d3436); }
        .nav-icon.orange { background: linear-gradient(135deg, #fdcb6e, #e17055); }

        .nav-label {
            font-size: 12px;
            text-align: center;
            font-weight: 500;
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: space-around;
            padding: 15px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .bottom-nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .bottom-nav-item:hover {
            color: #00ffff;
            transform: scale(1.1);
        }

        .bottom-nav-icon {
            width: 30px;
            height: 30px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            margin-bottom: 4px;
        }

        .bottom-nav-icon.green { background: #00d2d3; }
        .bottom-nav-icon.gray { background: #636e72; }
        .bottom-nav-icon.blue { background: #74b9ff; }
        .bottom-nav-icon.dark { background: #2d3436; }

        /* Responsive Design */
        @media (max-width: 480px) {
            .container {
                padding: 15px 10px;
            }

            .character-showcase {
                height: 350px;
                margin: 70px 0 20px 0;
            }

            .game-cards {
                gap: 10px;
            }

            .nav-grid {
                gap: 15px;
            }

            .nav-icon {
                width: 55px;
                height: 55px;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .game-card, .nav-item {
            animation: fadeInUp 0.6s ease forwards;
        }

        .game-card:nth-child(2) { animation-delay: 0.1s; }
        .game-card:nth-child(3) { animation-delay: 0.2s; }
        .game-card:nth-child(4) { animation-delay: 0.3s; }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div class="time">03:02 AM</div>
        <div class="battery-info">
            <div class="wifi-icon"></div>
            <span>60%</span>
            <div class="battery-icon"></div>
        </div>
    </div>

    <div class="container">
        <!-- Store Button -->
        <button class="store-btn">
            🛒 Store
        </button>

        <!-- Character Showcase -->
        <div class="character-showcase">
            <img src="https://via.placeholder.com/400x400/ff6b6b/ffffff?text=Character" alt="Game Character" class="character-image">
            <div class="carousel-dots">
                <div class="dot active"></div>
                <div class="dot"></div>
                <div class="dot"></div>
                <div class="dot"></div>
            </div>
        </div>

        <!-- Game Cards Section -->
        <div class="game-cards">
            <div class="game-card">
                <h3>🎮 Gallery</h3>
                <p>View character collection</p>
            </div>
            <div class="game-card">
                <h3>📖 Story</h3>
                <p>Continue your adventure</p>
                <div class="notification-badge">!</div>
            </div>
            <div class="game-card large">
                <h3>🎯 Stage</h3>
                <p>Battle challenges await</p>
                <div class="notification-badge">!</div>
            </div>
        </div>

        <!-- Navigation Grid -->
        <div class="nav-grid">
            <a href="#" class="nav-item">
                <div class="nav-icon red">💬</div>
                <div class="nav-label">Palrang Talk</div>
                <div class="notification-badge">6</div>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon pink">💝</div>
                <div class="nav-label">Palrang Plus</div>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon blue">🛡️</div>
                <div class="nav-label">Vanguard Squad</div>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon purple">👥</div>
                <div class="nav-label">Vanguards</div>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon green">👤</div>
                <div class="nav-label">Aide</div>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon gray">📚</div>
                <div class="nav-label">Encyclopedia</div>
                <div class="notification-badge">!</div>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon blue">📋</div>
                <div class="nav-label">Checklist</div>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon orange">🎒</div>
                <div class="nav-label">Bag</div>
            </a>
        </div>
    </div>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <a href="#" class="bottom-nav-item">
            <div class="bottom-nav-icon green">📞</div>
        </a>
        <a href="#" class="bottom-nav-item">
            <div class="bottom-nav-icon gray">📢</div>
        </a>
        <a href="#" class="bottom-nav-item">
            <div class="bottom-nav-icon blue">✉️</div>
        </a>
        <a href="#" class="bottom-nav-item">
            <div class="bottom-nav-icon dark">⚙️</div>
        </a>
    </div>

    <script>
        // Carousel functionality
        const dots = document.querySelectorAll('.dot');
        const characterImage = document.querySelector('.character-image');

        const images = [
            'https://via.placeholder.com/400x400/ff6b6b/ffffff?text=Character+1',
            'https://via.placeholder.com/400x400/4ecdc4/ffffff?text=Character+2',
            'https://via.placeholder.com/400x400/45b7d1/ffffff?text=Character+3',
            'https://via.placeholder.com/400x400/96ceb4/ffffff?text=Character+4'
        ];

        dots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                // Remove active class from all dots
                dots.forEach(d => d.classList.remove('active'));
                // Add active class to clicked dot
                dot.classList.add('active');
                // Change character image
                characterImage.src = images[index];
            });
        });

        // Auto-rotate carousel
        let currentIndex = 0;
        setInterval(() => {
            currentIndex = (currentIndex + 1) % images.length;
            dots.forEach(d => d.classList.remove('active'));
            dots[currentIndex].classList.add('active');
            characterImage.src = images[currentIndex];
        }, 5000);

        // Add click handlers for game cards
        document.querySelectorAll('.game-card').forEach(card => {
            card.addEventListener('click', () => {
                const title = card.querySelector('h3').textContent;
                alert(`Opening ${title}...`);
            });
        });

        // Add click handlers for navigation items
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const label = item.querySelector('.nav-label').textContent;
                alert(`Opening ${label}...`);
            });
        });

        // Store button functionality
        document.querySelector('.store-btn').addEventListener('click', () => {
            alert('Opening Store...');
        });
    </script>
</body>
</html>