/* Visual Novel style (mobile-first, vertical) */
:root{
  --vn-bg:#0a0d12;
  --vn-panel: rgba(15,20,28,.85);
  --vn-border: rgba(255,255,255,.12);
  --vn-text:#f1f3f7;
  --vn-muted:#9ca3af;
  --vn-accent:#3b82f6;
  --vn-accent-hover:#2563eb;
  --vn-gold:#fbbf24;
  --vn-shadow: 0 8px 32px rgba(0,0,0,.4);
}
*{box-sizing:border-box}
html,body{height:100%}
body{margin:0;background:linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);color:var(--vn-text);font-family:"Poppins",system-ui,-apple-system,Segoe UI,Roboto,Arial,sans-serif;overflow:hidden}

.vn-root{width:100vw;height:100dvh;display:grid;place-items:center;padding:0}
.vn-stage{position:relative;width:100%;max-width:480px;height:100dvh;background:#0f172a;border:none;border-radius:0;overflow:hidden;box-shadow:var(--vn-shadow)}
@media(min-width:768px){
  .vn-stage{aspect-ratio:9/16;height:auto;max-height:95vh;max-width:520px;border-radius:20px;border:1px solid var(--vn-border)}
  .vn-root{padding:20px}
}

.vn-bg{position:absolute;inset:0;background:linear-gradient(135deg, rgba(30,41,59,0.3) 0%, rgba(51,65,85,0.3) 50%, rgba(30,41,59,0.3) 100%), url('assets/background/taman%20paradise%20(1).png') center/cover no-repeat;filter:saturate(.9) brightness(.8);z-index:0}
.vn-dim{position:absolute;inset:0;background:linear-gradient(180deg,rgba(15,23,42,.1) 0%,rgba(15,23,42,.7) 60%, rgba(15,23,42,.95) 100%);z-index:1}

/* UI top-right */
.vn-topbar{position:absolute;right:12px;top:12px;display:flex;gap:8px;z-index:5;font-weight:600}
.vn-topbar .chip{
  font-size:11px;
  letter-spacing:.4px;
  padding:8px 14px;
  border-radius:20px;
  background:var(--vn-panel);
  border:1px solid var(--vn-border);
  color:#e2e8f0;
  cursor:pointer;
  transition:all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter:blur(12px);
  box-shadow:0 4px 16px rgba(0,0,0,.2);
  position:relative;
  overflow:hidden;
}
.vn-topbar .chip:hover{
  background:rgba(59,130,246,.15);
  border-color:var(--vn-accent);
  transform:translateY(-1px);
  box-shadow:0 6px 20px rgba(59,130,246,.3);
}
.vn-topbar .chip.is-active{
  background:linear-gradient(135deg,var(--vn-accent),var(--vn-accent-hover));
  color:#ffffff;
  border-color:transparent;
  box-shadow:0 6px 20px rgba(59,130,246,.4);
}
.vn-topbar .chip::before{
  content:'';
  position:absolute;
  top:0;
  left:-100%;
  width:100%;
  height:100%;
  background:linear-gradient(90deg,transparent,rgba(255,255,255,.1),transparent);
  transition:left 0.5s;
}
.vn-topbar .chip:hover::before{left:100%}

/* Character sprite */
.vn-character{position:absolute;left:0;bottom:0;top:0;width:80%;display:grid;place-items:end center;padding:0 0 10px;z-index:2}
.vn-character img{
  max-width:100%;
  max-height:100%;
  object-fit:contain;
  filter:drop-shadow(0 20px 60px rgba(0,0,0,.6)) drop-shadow(0 8px 24px rgba(0,0,0,.4));
  transition:transform 0.3s ease;
  cursor:pointer;
}
.vn-character img:hover{transform:scale(1.02)}

/* Click effect animation */
@keyframes clickRipple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

.click-effect {
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(59,130,246,0.6) 50%, transparent 100%);
  pointer-events: none;
  animation: clickRipple 0.6s ease-out;
  z-index: 10;
}

/* Dialogue panel */
.vn-dialog{position:absolute;left:0;right:0;bottom:0;padding:16px;z-index:4}
.vn-dialog .panel{
  background:rgba(8,12,20,.75);
  border:1px solid rgba(255,255,255,.08);
  border-radius:16px;
  padding:16px 18px 20px;
  backdrop-filter:blur(20px);
  box-shadow:var(--vn-shadow);
  position:relative;
}
.vn-name{
  font-weight:700;
  margin-bottom:8px;
  display:flex;
  align-items:center;
  gap:10px;
  font-size:14px;
  color:#f8fafc;
}
.vn-name .bar{
  width:4px;
  height:16px;
  background:linear-gradient(135deg,var(--vn-accent),var(--vn-gold));
  border-radius:2px;
  box-shadow:0 2px 8px rgba(59,130,246,.3);
}
.vn-text{
  line-height:1.6;
  min-height:3.2lh;
  color:var(--vn-text);
  font-size:14px;
  letter-spacing:0.3px;
}
.vn-next{
  position:absolute;
  right:20px;
  bottom:16px;
  color:var(--vn-accent);
  font-size:16px;
  opacity:.9;
  animation:pulse 2s infinite;
}
@keyframes pulse{0%,100%{opacity:.9} 50%{opacity:.5}}

/* Choice box - styled like the reference image */
.vn-choices{
  position:absolute;
  left:50%;
  bottom:180px;
  transform:translateX(-50%);
  display:grid;
  gap:8px;
  width:min(92%,400px);
  z-index:6;
  transition:all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}
.vn-choices.show{
  animation:choicesSlideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}
.vn-choice{
  background:linear-gradient(135deg, rgba(0,40,80,.85) 0%, rgba(0,60,120,.85) 100%);
  border:2px solid rgba(100,200,255,.3);
  padding:12px 20px;
  border-radius:8px;
  color:#ffffff;
  text-align:center;
  cursor:pointer;
  transition:all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter:blur(15px);
  box-shadow:0 4px 20px rgba(0,40,80,.4), inset 0 1px 0 rgba(255,255,255,.1);
  font-size:14px;
  font-weight:500;
  line-height:1.4;
  position:relative;
  overflow:hidden;
  min-height:48px;
  display:flex;
  align-items:center;
  justify-content:center;
}
.vn-choice:hover{
  background:linear-gradient(135deg, rgba(0,60,120,.9) 0%, rgba(0,80,160,.9) 100%);
  border-color:rgba(150,220,255,.6);
  transform:translateY(-2px);
  box-shadow:0 6px 25px rgba(0,60,120,.5), inset 0 1px 0 rgba(255,255,255,.2);
}
.vn-choice:active{
  transform:translateY(0) scale(0.98);
  transition:transform 0.1s;
}

/* Click effect for choices */
.vn-choice {
  position: relative;
  overflow: hidden;
}

.choice-click-effect {
  position: absolute;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255,255,255,0.6) 0%, rgba(255,255,255,0.2) 50%, transparent 100%);
  pointer-events: none;
  animation: choiceClickRipple 0.5s ease-out;
  z-index: 1;
}

@keyframes choiceClickRipple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(3);
    opacity: 0;
  }
}
.vn-choice::before{
  content:'';
  position:absolute;
  top:0;
  left:-100%;
  width:100%;
  height:100%;
  background:linear-gradient(90deg,transparent,rgba(255,255,255,.15),transparent);
  transition:left 0.6s;
}
.vn-choice:hover::before{left:100%}
.vn-choice::after{
  content:'';
  position:absolute;
  inset:0;
  border-radius:6px;
  background:linear-gradient(135deg, transparent 0%, rgba(255,255,255,.05) 50%, transparent 100%);
  pointer-events:none;
}
@keyframes choicesSlideUp{
  from{
    opacity:0;
    transform:translateX(-50%) translateY(30px);
  }
  to{
    opacity:1;
    transform:translateX(-50%) translateY(0);
  }
}

/* Log modal */
.vn-log{
  position:absolute;
  inset:0;
  background:rgba(0,0,0,.7);
  backdrop-filter:blur(8px);
  display:none;
  z-index:8;
  align-items:flex-end;
  animation:fadeIn 0.3s ease;
}
.vn-log.show{display:flex}
.vn-log .sheet{
  width:100%;
  max-height:75%;
  overflow:auto;
  background:var(--vn-panel);
  border-top-left-radius:20px;
  border-top-right-radius:20px;
  border:1px solid var(--vn-border);
  padding:20px;
  backdrop-filter:blur(20px);
  box-shadow:0 -8px 32px rgba(0,0,0,.4);
  animation:slideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Responsive log modal */
@media(max-width:480px){
  .vn-log .sheet{
    max-height:80%;
    padding:16px;
    border-top-left-radius:16px;
    border-top-right-radius:16px;
  }
  .vn-log h4{font-size:15px;margin-bottom:12px}
  .vn-log .line{font-size:12px;padding:8px 10px}
  .vn-log .close{
    right:12px;
    top:12px;
    padding:6px 12px;
    font-size:11px;
  }
}

@media(max-width:360px){
  .vn-log .sheet{
    max-height:85%;
    padding:14px;
  }
  .vn-log h4{font-size:14px}
  .vn-log .line{font-size:11px;padding:7px 9px}
  .vn-log .close{padding:5px 10px;font-size:10px}
}
.vn-log h4{
  margin:0 0 16px 0;
  color:var(--vn-text);
  font-size:16px;
  font-weight:700;
  display:flex;
  align-items:center;
  gap:8px;
}
.vn-log h4::before{
  content:'📜';
  font-size:18px;
}
.vn-log .line{
  color:var(--vn-text);
  font-size:13px;
  padding:10px 12px;
  border-bottom:1px solid rgba(255,255,255,.06);
  border-radius:8px;
  margin-bottom:4px;
  background:rgba(255,255,255,.02);
  line-height:1.5;
}
.vn-log .line:last-child{border-bottom:none}

/* Special styling for choice entries in log */
.vn-log .line:contains("Pilihan"){
  background:rgba(59,130,246,.1);
  border-left:3px solid var(--vn-accent);
  font-style:italic;
}
.vn-log .line:contains("Dipilih"){
  background:rgba(34,197,94,.1);
  border-left:3px solid #22c55e;
  font-weight:600;
}
.vn-log .close{
  position:absolute;
  right:16px;
  top:16px;
  background:var(--vn-accent);
  border:none;
  padding:8px 16px;
  border-radius:20px;
  cursor:pointer;
  color:#ffffff;
  font-weight:600;
  font-size:12px;
  transition:all 0.3s ease;
  box-shadow:0 4px 16px rgba(59,130,246,.3);
}
.vn-log .close:hover{
  background:var(--vn-accent-hover);
  transform:translateY(-1px);
  box-shadow:0 6px 20px rgba(59,130,246,.4);
}
@keyframes fadeIn{from{opacity:0} to{opacity:1}}
@keyframes slideUp{from{transform:translateY(100%)} to{transform:translateY(0)}}



/* Responsive design for various smartphones */

/* Large phones (iPhone 14 Pro Max, Samsung Galaxy S23 Ultra) */
@media(max-width:480px) and (min-width:415px){
  .vn-stage{max-width:100%;height:100dvh}
  .vn-topbar{right:12px;top:12px;gap:8px}
  .vn-topbar .chip{padding:8px 12px;font-size:11px}
  .vn-dialog{padding:16px}
  .vn-dialog .panel{padding:14px 16px 18px}
  .vn-text{font-size:14px;line-height:1.6}
  .vn-name{font-size:14px}
  .vn-choices{width:min(92%,400px);gap:8px;bottom:180px}
  .vn-choice{padding:12px 18px;font-size:14px;min-height:48px}
  .vn-character{width:78%;padding:0 0 8px}
  .vn-character img{max-width:100%;max-height:100%}
}

/* Standard phones (iPhone 14, Samsung Galaxy S23) */
@media(max-width:414px) and (min-width:376px){
  .vn-stage{max-width:100%;height:100dvh}
  .vn-topbar{right:10px;top:10px;gap:7px}
  .vn-topbar .chip{padding:7px 11px;font-size:10px}
  .vn-dialog{padding:14px}
  .vn-dialog .panel{padding:13px 15px 17px}
  .vn-text{font-size:13px;line-height:1.5}
  .vn-name{font-size:13px}
  .vn-choices{width:min(94%,380px);gap:7px;bottom:175px}
  .vn-choice{padding:11px 16px;font-size:13px;min-height:46px}
  .vn-character{width:80%;padding:0 0 8px}
  .vn-character img{max-width:100%;max-height:100%}
}

/* Compact phones (iPhone SE, smaller Android) */
@media(max-width:375px) and (min-width:321px){
  .vn-stage{max-width:100%;height:100dvh}
  .vn-topbar{right:8px;top:8px;gap:6px}
  .vn-topbar .chip{padding:6px 10px;font-size:9px}
  .vn-dialog{padding:12px}
  .vn-dialog .panel{padding:12px 14px 16px}
  .vn-text{font-size:12px;line-height:1.5}
  .vn-name{font-size:12px}
  .vn-choices{width:min(95%,360px);gap:6px;bottom:170px}
  .vn-choice{padding:10px 14px;font-size:12px;min-height:44px}
  .vn-character{width:82%;padding:0 0 6px}
  .vn-character img{max-width:100%;max-height:100%}
}

/* Very small phones */
@media(max-width:320px){
  .vn-stage{max-width:100%;height:100dvh}
  .vn-topbar{right:6px;top:6px;gap:4px}
  .vn-topbar .chip{padding:5px 8px;font-size:8px}
  .vn-dialog{padding:10px}
  .vn-dialog .panel{padding:10px 12px 14px}
  .vn-text{font-size:11px;line-height:1.4}
  .vn-name{font-size:11px}
  .vn-choices{width:min(96%,300px);gap:5px;bottom:165px}
  .vn-choice{padding:9px 12px;font-size:11px;min-height:40px}
  .vn-character{width:84%;padding:0 0 4px}
  .vn-character img{max-width:100%;max-height:100%}
}

/* Utility */
.hidden{display:none !important}
.disable-tap{pointer-events:none}

/* Tall phones (iPhone 14 Pro, Galaxy S23) - aspect ratio > 2:1 */
@media(max-width:480px) and (min-aspect-ratio:2/1){
  .vn-character{width:75%;padding:0 0 12px}
  .vn-character img{max-height:100%}
  .vn-choices{bottom:185px}
  .vn-dialog{padding:18px}
}

/* Wide phones in portrait (older iPhones, some Android) */
@media(max-width:480px) and (max-aspect-ratio:16/9){
  .vn-character{width:85%;padding:0 0 6px}
  .vn-character img{max-height:100%}
  .vn-choices{bottom:165px}
  .vn-dialog{padding:12px}
}

/* Landscape orientation adjustments */
@media(max-height:480px) and (orientation:landscape){
  .vn-stage{height:100vh;max-height:none}
  .vn-character{width:55%;padding:0 0 3px}
  .vn-character img{max-height:100%}
  .vn-choices{bottom:120px;width:min(85%,350px)}
  .vn-choice{padding:8px 14px;min-height:36px;font-size:12px}
  .vn-dialog{padding:8px}
  .vn-dialog .panel{padding:8px 12px 12px}
  .vn-text{font-size:12px}
  .vn-topbar{right:8px;top:8px}
  .vn-topbar .chip{padding:4px 8px;font-size:9px}
}

/* Safe area for notched devices */
@supports(padding:max(0px)){
  .vn-stage{padding-top:max(12px,env(safe-area-inset-top))}
  .vn-dialog{padding-bottom:max(16px,env(safe-area-inset-bottom))}
  .vn-topbar{top:max(12px,env(safe-area-inset-top))}
}

/* High DPI displays */
@media(-webkit-min-device-pixel-ratio:2), (min-resolution:192dpi){
  .vn-choice{border-width:1px}
  .vn-dialog .panel{border-width:1px}
  .vn-topbar .chip{border-width:1px}
}

/* Touch optimization */
@media(pointer:coarse){
  .vn-choice{min-height:48px;padding:12px 16px}
  .vn-topbar .chip{min-height:32px;min-width:48px}
  .vn-character img{cursor:pointer}
}

/* Hover capability detection */
@media(hover:hover){
  .vn-choice:hover{transform:translateY(-2px) scale(1.02)}
  .vn-topbar .chip:hover{transform:translateY(-1px)}
}

